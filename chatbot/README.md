# HRMS Chatbot

An AI-powered chatbot for the HRMS platform that uses OpenAI for natural language processing and can perform various HR-related actions on behalf of users.

## Features

- **Natural Language Processing**: Uses OpenAI GPT models for understanding user queries
- **Permission-based Actions**: Only executes actions that users have permission to perform
- **Leave Management**: Apply for leaves, check leave balance, view leave history
- **Project Information**: Get project summaries and basic project information
- **Employee Information**: Access employee details and reporting structure
- **Attendance**: Check attendance records
- **Chat History**: Persistent chat sessions with message history
- **Real-time Interface**: AJAX-based chat interface with typing indicators

## Setup

### 1. Install Dependencies

The chatbot requires the OpenAI Python package:

```bash
pip install openai
```

### 2. Environment Variables

Add the following environment variables to your `.env` file:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
CHATBOT_MAX_TOKENS=1000
CHATBOT_TEMPERATURE=0.7
```

### 3. Database Migration

The chatbot app is already added to `INSTALLED_APPS` and migrations have been created. If you need to run them again:

```bash
python manage.py makemigrations chatbot
python manage.py migrate
```

### 4. Test the Setup

Run the test command to verify everything is working:

```bash
python manage.py test_chatbot
```

## Usage

### Web Interface

1. Navigate to `/chatbot/` to access the main chat interface
2. Use `/chatbot/widget/` for the embeddable chat widget

### API Endpoints

The chatbot provides REST API endpoints:

- `POST /api/chatbot/chat/` - Send a message and get AI response
- `GET /api/chatbot/sessions/` - List user's chat sessions
- `GET /api/chatbot/sessions/{session_id}/` - Get specific session details
- `GET /api/chatbot/sessions/{session_id}/messages/` - Get messages for a session
- `POST /api/chatbot/sessions/` - Create a new chat session

### Available Actions

The chatbot can perform the following actions based on user permissions:

1. **Apply for Leave**
   - Example: "I want to apply for sick leave from December 25 to December 27 because I'm not feeling well"

2. **Check Leave Balance**
   - Example: "What's my current leave balance?"

3. **Get Project Summary**
   - Example: "Show me my current projects" or "Give me details about project ID 5"

4. **View Employee Information**
   - Example: "Show me my profile information"

5. **Check Attendance**
   - Example: "Show me my attendance for this month"

## Architecture

### Models

- **ChatSession**: Stores chat sessions for users
- **ChatMessage**: Individual messages in a chat session
- **ChatAction**: Tracks actions performed by the chatbot
- **ChatbotSettings**: User-specific chatbot preferences

### Services

- **OpenAIService**: Handles OpenAI API integration and response generation
- **ChatbotActionProcessor**: Processes and executes user-requested actions

### Permissions

- **ChatbotPermissionChecker**: Validates user permissions before executing actions

## Customization

### Adding New Actions

1. Add the action to `ChatbotPermissionChecker` in `permissions.py`
2. Add the function definition to `OpenAIService._get_available_tools()`
3. Implement the action handler in `ChatbotActionProcessor`
4. Update the action choices in the `ChatAction` model

### Modifying AI Behavior

Edit the system context in `OpenAIService._build_system_context()` to change how the AI responds to users.

### Styling

Customize the chat interface by modifying:
- `static/chatbot/css/chat.css` - Main styles
- `templates/chatbot/chat_interface.html` - Main interface template
- `templates/chatbot/chat_widget.html` - Widget template

## Security Considerations

1. **API Key Security**: Keep your OpenAI API key secure and never commit it to version control
2. **Permission Checks**: All actions are validated against user permissions
3. **Input Validation**: User inputs are validated before processing
4. **Rate Limiting**: Consider implementing rate limiting for API endpoints

## Troubleshooting

### Common Issues

1. **OpenAI API Errors**: Check your API key and ensure you have sufficient credits
2. **Permission Denied**: Verify user has appropriate permissions for requested actions
3. **Import Errors**: Ensure all dependencies are installed

### Debug Mode

Enable Django debug mode to see detailed error messages during development.

### Logs

Check Django logs for chatbot-related errors. The chatbot uses the logger name `chatbot.services`.

## Future Enhancements

- Integration with project_horilla for advanced project and task management
- Voice input/output capabilities
- Multi-language support
- Advanced analytics and reporting
- Integration with external calendar systems
- Automated workflow triggers

## Support

For issues and questions, please check the Django admin interface for chatbot models and review the logs for any error messages.
